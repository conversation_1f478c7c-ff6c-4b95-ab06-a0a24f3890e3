#!/usr/bin/env python3
"""
Export Manager for ClipsMore
Handles export operations for clipboard data in various formats.
"""

import os
import sys
import json
import csv
import sqlite3
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from DB.db_connection import ConnectionPoolManager


class ExportError(Exception):
    """Custom exception for export operations."""
    pass


class ExportManager:
    """
    Core export manager for ClipsMore application.
    Handles export operations for clipboard data in various formats.
    """
    
    def __init__(self, database_manager=None):
        """Initialize the export manager."""
        print('[DEBUG] ExportManager.__init__ called')
        self.database_manager = database_manager
        self.connection_pool = ConnectionPoolManager()
        self.format_handlers = {}
        self.progress_callback = None
        self.cancel_requested = False

        # Initialize format handlers
        self._initialize_format_handlers()
    
    def _initialize_format_handlers(self):
        """Initialize available format handlers."""
        print('[DEBUG] ExportManager._initialize_format_handlers called')

        # Import and register format handlers
        from export.format_handlers.json_handler import JSONHandler
        from export.format_handlers.csv_handler import CSVHandler
        from export.format_handlers.html_handler import HTMLHandler
        from export.format_handlers.xml_handler import XMLHandler

        self.register_format_handler('json', JSONHandler())
        self.register_format_handler('csv', CSVHandler())
        self.register_format_handler('html', HTMLHandler())
        self.register_format_handler('xml', XMLHandler())
    
    def register_format_handler(self, format_type: str, handler):
        """Register a format handler for a specific export format."""
        print(f'[DEBUG] ExportManager.register_format_handler called for {format_type}')
        self.format_handlers[format_type] = handler
    
    def get_available_formats(self) -> List[str]:
        """Get list of available export formats."""
        print('[DEBUG] ExportManager.get_available_formats called')
        return list(self.format_handlers.keys())
    
    def set_progress_callback(self, callback: Callable[[int, str], None]):
        """Set callback function for progress updates."""
        print('[DEBUG] ExportManager.set_progress_callback called')
        self.progress_callback = callback
    
    def cancel_export(self):
        """Cancel the current export operation."""
        print('[DEBUG] ExportManager.cancel_export called')
        self.cancel_requested = True
    
    def _update_progress(self, percentage: int, message: str = ""):
        """Update export progress."""
        if self.progress_callback:
            self.progress_callback(percentage, message)
    
    def _check_cancellation(self):
        """Check if export has been cancelled."""
        if self.cancel_requested:
            raise ExportError("Export operation was cancelled")
    
    def collect_export_data(self, selection_criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Collect data for export based on selection criteria.
        
        Args:
            selection_criteria: Dictionary containing filter criteria
                - date_from: Start date for filtering
                - date_to: End date for filtering
                - business_cases: List of business case IDs
                - components: List of component IDs
                - include_unassigned: Boolean to include unassigned clips
                - content_filter: Text filter for content
        
        Returns:
            List of dictionaries containing clip data
        """
        print('[DEBUG] ExportManager.collect_export_data called')
        self._update_progress(10, "Collecting export data...")
        
        try:
            with self.connection_pool.get_connection() as conn:
                cursor = conn.cursor()
                
                # Build query based on selection criteria
                query = """
                    SELECT 
                        cm.transaction_id,
                        cm.clip_id,
                        cm.alias,
                        c.content,
                        c.timestamp,
                        mb.bus_case,
                        mc.bus_component,
                        cm.more_bus_id,
                        cm.more_comp_id
                    FROM clipsmore_tbl cm
                    LEFT JOIN clips_tbl c ON cm.clip_id = c.id
                    LEFT JOIN more_bus_tbl mb ON cm.more_bus_id = mb.more_bus_id
                    LEFT JOIN more_comp_tbl mc ON cm.more_comp_id = mc.more_comp_id
                """
                
                conditions = []
                params = []
                
                # Apply date filters
                if selection_criteria.get('date_from'):
                    conditions.append("c.timestamp >= ?")
                    params.append(selection_criteria['date_from'])
                
                if selection_criteria.get('date_to'):
                    conditions.append("c.timestamp <= ?")
                    params.append(selection_criteria['date_to'])
                
                # Apply business case filter
                if selection_criteria.get('business_cases'):
                    placeholders = ','.join(['?' for _ in selection_criteria['business_cases']])
                    conditions.append(f"cm.more_bus_id IN ({placeholders})")
                    params.extend(selection_criteria['business_cases'])
                
                # Apply component filter
                if selection_criteria.get('components'):
                    placeholders = ','.join(['?' for _ in selection_criteria['components']])
                    conditions.append(f"cm.more_comp_id IN ({placeholders})")
                    params.extend(selection_criteria['components'])
                
                # Apply content filter
                if selection_criteria.get('content_filter'):
                    conditions.append("c.content LIKE ?")
                    params.append(f"%{selection_criteria['content_filter']}%")
                
                # Include unassigned clips if requested
                if not selection_criteria.get('include_unassigned', True):
                    conditions.append("(cm.more_bus_id IS NOT NULL OR cm.more_comp_id IS NOT NULL)")
                
                # Add WHERE clause if conditions exist
                if conditions:
                    query += " WHERE " + " AND ".join(conditions)
                
                # Add ordering
                query += " ORDER BY c.timestamp DESC"
                
                self._check_cancellation()
                self._update_progress(30, "Executing database query...")
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
                self._check_cancellation()
                self._update_progress(50, "Processing query results...")
                
                # Convert to list of dictionaries
                columns = [desc[0] for desc in cursor.description]
                data = []
                
                for i, row in enumerate(rows):
                    if i % 100 == 0:  # Update progress every 100 records
                        self._check_cancellation()
                        progress = 50 + (i / len(rows)) * 30
                        self._update_progress(int(progress), f"Processing record {i+1}/{len(rows)}")
                    
                    record = dict(zip(columns, row))
                    data.append(record)
                
                self._update_progress(80, f"Collected {len(data)} records for export")
                return data
                
        except sqlite3.Error as e:
            raise ExportError(f"Database error during data collection: {e}")
        except Exception as e:
            raise ExportError(f"Error collecting export data: {e}")
    
    def export_data(self, format_type: str, selection_criteria: Dict[str, Any], 
                   output_path: str, export_config: Dict[str, Any] = None) -> bool:
        """
        Export clipboard data in the specified format.
        
        Args:
            format_type: Export format (json, csv, html, xml, txt)
            selection_criteria: Data selection criteria
            output_path: Output file path
            export_config: Additional export configuration
        
        Returns:
            True if export successful, False otherwise
        """
        print(f'[DEBUG] ExportManager.export_data called for format {format_type}')
        
        try:
            self.cancel_requested = False
            self._update_progress(0, "Starting export...")
            
            # Check if format handler is available
            if format_type not in self.format_handlers:
                raise ExportError(f"Export format '{format_type}' is not supported")
            
            # Collect data for export
            data = self.collect_export_data(selection_criteria)
            
            self._check_cancellation()
            self._update_progress(85, "Formatting data for export...")
            
            # Get format handler and export data
            handler = self.format_handlers[format_type]
            success = handler.export(data, output_path, export_config or {})
            
            if success:
                self._update_progress(100, f"Export completed successfully: {output_path}")
                self._save_export_template(format_type, selection_criteria, export_config)
                return True
            else:
                raise ExportError("Export handler returned failure")
                
        except ExportError:
            raise
        except Exception as e:
            raise ExportError(f"Unexpected error during export: {e}")
    
    def _save_export_template(self, format_type: str, selection_criteria: Dict[str, Any], 
                             export_config: Dict[str, Any]):
        """Save export configuration as a template for reuse."""
        print('[DEBUG] ExportManager._save_export_template called')
        # This will be implemented when the database schema is updated
        pass
    
    def get_export_templates(self) -> List[Dict[str, Any]]:
        """Get saved export templates."""
        print('[DEBUG] ExportManager.get_export_templates called')
        # This will be implemented when the database schema is updated
        return []
    
    def validate_export_path(self, output_path: str) -> bool:
        """Validate that the export path is writable."""
        print(f'[DEBUG] ExportManager.validate_export_path called for {output_path}')
        
        try:
            # Check if directory exists and is writable
            directory = os.path.dirname(output_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
            
            # Test write access
            test_file = output_path + '.test'
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            
            return True
            
        except Exception as e:
            print(f'[ERROR] Export path validation failed: {e}')
            return False
